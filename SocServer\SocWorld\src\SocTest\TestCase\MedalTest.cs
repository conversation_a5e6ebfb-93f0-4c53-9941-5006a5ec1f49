using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;

namespace WizardGames.Soc.Test
{
    public class MedalTest : TestCaseBase
    {
        PlayerEntity player;
        public override void Run(ArraySegment<string> args)
        {
            player = CreatePlayerWithInventory();
            AddEntityOnlyInitComponents(player);
            ServerConfig.Instance.ClosePushToLobby = 1;
        }

        private static void SetupMedalConfig(long medalId, long medalLevel, long styleId, long taskId, bool shouldPreCount)
        {
            var medalJson = @"{
      ""medalID"": " + medalId + @",
      ""level"": " + medalLevel + @",
      ""type"": 3,
      ""styleID"": " + styleId + @",
      ""task"": [
        [
          2,
          " + taskId + @"
        ]
      ],
      ""styleRankPoints"": [
        [
          1,
          10
        ]
      ],
      ""rankID"": 1,
      ""shouldPreCount"": " + shouldPreCount.ToString().ToLower() + @"  
    }";
            McCommon.Tables.TBMedal.Update(SimpleJSON.JSONNode.Parse(medalJson));
        }

        public override void Cleanup()
        {
            base.Cleanup();
        }
    }
}