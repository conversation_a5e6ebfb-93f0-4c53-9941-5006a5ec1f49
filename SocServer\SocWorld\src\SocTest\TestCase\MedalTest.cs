using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;

namespace WizardGames.Soc.Test
{
    public class MedalTest : TestCaseBase
    {
        PlayerEntity player;
        public override void Run(ArraySegment<string> args)
        {
            player = CreatePlayerWithInventory();
            AddEntityOnlyInitComponents(player);
            ServerConfig.Instance.ClosePushToLobby = 1;
        }

        private static void SetupMedalConfig(long medalId, long medalLevel, long styleId, long taskId, bool shouldPreCount)
        {
            var medalJson = @"{
      ""medalID"": " + medalId + @",
      ""level"": " + medalLevel + @",
      ""type"": 3,
      ""styleID"": " + styleId + @",
      ""task"": [
        [
          2,
          " + taskId + @"
        ]
      ],
      ""styleRankPoints"": [
        [
          1,
          10
        ]
      ],
      ""rankID"": 1,
      ""shouldPreCount"": " + shouldPreCount.ToString().ToLower() + @"
    }";
            McCommon.Tables.TBMedal.Update(SimpleJSON.JSONNode.Parse(medalJson));
        }

        private static void SetupMedalTaskConfig(long taskId, int endCondition, long[] endConditionParameter = null,
            int taskType = 8, string taskPhaseDescribe = "", string taskDescribe = "", string taskLongDescribe = "",
            int[] taskRewardId = null, bool autoGrant = false, int[] showRewardId = null, int taskModule = 2,
            string getsource = "", bool isteam = false, int completeTime = 0, bool isHide = false,
            int counterShowType = 1, int endConditionMode = 0, int resetCondition = 0, long[] resetConditionParameter = null,
            int[] subTasks = null, int[] endEvent = null, int[] beginEvent = null, long[] beginConditionParameter = null,
            int taskPhaseBeginCondition = 0, int isSubTask = 0, int taskNumber = 0, int[] beginEvent1 = null,
            int[] beginEvent2 = null, int[] endEvent1 = null, int[] endEvent2 = null, long[] searchIds = null,
            int markerType = 0, int goalDisplay = 0, int guideId = 0, int subtitleId = 0, long timelineId = 0,
            int[] guideItem = null, int guideBlueprint = 0, int trackingPriority = 0, int[] unLockHudElemIds = null,
            string cartoonVideoUrl = "", string cartoonAudio = "", string uiName = "", int guideLineID = 0,
            int[] unHideHudElemIds = null, int[] hudWhiteListWhenTimeline = null, string[] windowWhiteListWhenTimeline = null,
            bool eventMove = false, string openWin = "", int effectID = 0, string bgAudio = "", long modelId = 0,
            string npcHUDGuide = "")
        {
            var taskJson = @"{
      ""id"": " + taskId + @",
      ""type"": " + taskType + @",
      ""taskId"": " + (taskId > int.MaxValue ? 0 : (int)taskId) + @",
      ""taskNumber"": " + taskNumber + @",
      ""taskPhaseBeginCondition"": " + taskPhaseBeginCondition + @",
      ""isSubTask"": " + isSubTask + @",";

            // Only add non-empty/non-default arrays and values
            if (beginConditionParameter != null && beginConditionParameter.Length > 0)
                taskJson += @"""beginConditionParameter"": [" + string.Join(",", beginConditionParameter) + @"],";
            else
                taskJson += @"""beginConditionParameter"": [],";

            if (beginEvent != null && beginEvent.Length > 0)
                taskJson += @"""beginEvent"": [" + string.Join(",", beginEvent) + @"],";
            else
                taskJson += @"""beginEvent"": [],";

            if (beginEvent1 != null && beginEvent1.Length > 0)
                taskJson += @"""beginEvent1"": [" + string.Join(",", beginEvent1) + @"],";
            else
                taskJson += @"""beginEvent1"": [],";

            if (beginEvent2 != null && beginEvent2.Length > 0)
                taskJson += @"""beginEvent2"": [" + string.Join(",", beginEvent2) + @"],";
            else
                taskJson += @"""beginEvent2"": [],";

            // Add text descriptions with proper escaping
            taskJson += @"""taskPhaseDescribe"": {""index"": 0, ""text"": """ + taskPhaseDescribe.Replace("\"", "\\\"") + @"""},";
            taskJson += @"""taskDescribe"": {""index"": 0, ""text"": """ + taskDescribe.Replace("\"", "\\\"") + @"""},";
            taskJson += @"""taskLongDescribe"": {""index"": 0, ""text"": """ + taskLongDescribe.Replace("\"", "\\\"") + @"""},";

            taskJson += @"""isHide"": " + isHide.ToString().ToLower() + @",
      ""counterShowType"": " + counterShowType + @",
      ""taskPhaseEndCondition"": " + endCondition + @",";

            if (endConditionParameter != null && endConditionParameter.Length > 0)
                taskJson += @"""endConditionParameter"": [" + string.Join(",", endConditionParameter) + @"],";
            else
                taskJson += @"""endConditionParameter"": [],";

            taskJson += @"""endConditionMode"": " + endConditionMode + @",
      ""resetCondition"": " + resetCondition + @",";

            if (resetConditionParameter != null && resetConditionParameter.Length > 0)
                taskJson += @"""resetConditionParameter"": [" + string.Join(",", resetConditionParameter) + @"],";
            else
                taskJson += @"""resetConditionParameter"": [],";

            taskJson += @"""taskType"": " + taskType + @",";

            if (subTasks != null && subTasks.Length > 0)
                taskJson += @"""subTasks"": [" + string.Join(",", subTasks) + @"],";
            else
                taskJson += @"""subTasks"": [],";

            if (endEvent != null && endEvent.Length > 0)
                taskJson += @"""endEvent"": [" + string.Join(",", endEvent) + @"],";
            else
                taskJson += @"""endEvent"": [],";

            if (endEvent1 != null && endEvent1.Length > 0)
                taskJson += @"""endEvent1"": [" + string.Join(",", endEvent1) + @"],";
            else
                taskJson += @"""endEvent1"": [],";

            if (endEvent2 != null && endEvent2.Length > 0)
                taskJson += @"""endEvent2"": [" + string.Join(",", endEvent2) + @"],";
            else
                taskJson += @"""endEvent2"": [],";

            if (searchIds != null && searchIds.Length > 0)
                taskJson += @"""searchIds"": [" + string.Join(",", searchIds) + @"],";
            else
                taskJson += @"""searchIds"": [],";

            taskJson += @"""markerType"": " + markerType + @",
      ""goalDisplay"": " + goalDisplay + @",
      ""guideId"": " + guideId + @",
      ""subtitleId"": " + subtitleId + @",
      ""timelineId"": " + timelineId + @",";

            if (guideItem != null && guideItem.Length > 0)
                taskJson += @"""guideItem"": [" + string.Join(",", guideItem) + @"],";
            else
                taskJson += @"""guideItem"": [],";

            taskJson += @"""guideBlueprint"": " + guideBlueprint + @",
      ""completeTime"": " + completeTime + @",";

            if (taskRewardId != null && taskRewardId.Length > 0)
                taskJson += @"""taskRewardId"": [" + string.Join(",", taskRewardId) + @"],";
            else
                taskJson += @"""taskRewardId"": [],";

            taskJson += @"""autoGrant"": " + autoGrant.ToString().ToLower() + @",";

            if (showRewardId != null && showRewardId.Length > 0)
                taskJson += @"""showRewardId"": [" + string.Join(",", showRewardId) + @"],";
            else
                taskJson += @"""showRewardId"": [],";

            taskJson += @"""trackingPriority"": " + trackingPriority + @",
      ""taskModule"": " + taskModule + @",
      ""getsource"": {""index"": 0, ""text"": """ + getsource.Replace("\"", "\\\"") + @"""},
      ""isteam"": " + isteam.ToString().ToLower() + @",";

            if (unLockHudElemIds != null && unLockHudElemIds.Length > 0)
                taskJson += @"""unLockHudElemIds"": [" + string.Join(",", unLockHudElemIds) + @"],";
            else
                taskJson += @"""unLockHudElemIds"": [],";

            taskJson += @"""cartoonVideoUrl"": """ + cartoonVideoUrl.Replace("\"", "\\\"") + @""",
      ""cartoonAudio"": """ + cartoonAudio.Replace("\"", "\\\"") + @""",
      ""UiName"": """ + uiName.Replace("\"", "\\\"") + @""",
      ""guideLineID"": " + guideLineID + @",";

            if (unHideHudElemIds != null && unHideHudElemIds.Length > 0)
                taskJson += @"""unHideHudElemIds"": [" + string.Join(",", unHideHudElemIds) + @"],";
            else
                taskJson += @"""unHideHudElemIds"": [],";

            if (hudWhiteListWhenTimeline != null && hudWhiteListWhenTimeline.Length > 0)
                taskJson += @"""hudWhiteListWhenTimeline"": [" + string.Join(",", hudWhiteListWhenTimeline) + @"],";
            else
                taskJson += @"""hudWhiteListWhenTimeline"": [],";

            if (windowWhiteListWhenTimeline != null && windowWhiteListWhenTimeline.Length > 0)
                taskJson += @"""windowWhiteListWhenTimeline"": [""" + string.Join("\",\"", windowWhiteListWhenTimeline) + @"""],";
            else
                taskJson += @"""windowWhiteListWhenTimeline"": [],";

            taskJson += @"""eventDelayDic"": [],
      ""eventMove"": " + eventMove.ToString().ToLower() + @",
      ""openWin"": """ + openWin.Replace("\"", "\\\"") + @""",
      ""EffectID"": " + effectID + @",
      ""bgAudio"": """ + bgAudio.Replace("\"", "\\\"") + @""",
      ""modelId"": " + modelId + @",
      ""npcHUDGuide"": {""index"": 0, ""text"": """ + npcHUDGuide.Replace("\"", "\\\"") + @"""}
    }";

            McCommon.Tables.TbQuestPhase.Update(SimpleJSON.JSONNode.Parse(taskJson));
        }

        public override void Cleanup()
        {
            base.Cleanup();
        }
    }
}